/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        // Custom Neo-brutalism color palette
        'lemongrass': '#9ACD32',
        'olive': '#808000',
        'burnt-orange': '#CC5500',
        'light-bg': '#F9F9F9',
        'dark-text': '#000000',
        'secondary-text': '#333333',
      },
      fontFamily: {
        'sans': ['Inter', 'system-ui', 'sans-serif'],
        'display': ['Inter', 'system-ui', 'sans-serif'],
      },
      spacing: {
        // Ensure all spacing follows 4px grid system
        '18': '4.5rem',
        '22': '5.5rem',
        '26': '6.5rem',
        '30': '7.5rem',
      },
      borderRadius: {
        // Override all border radius to be sharp (none)
        'none': '0',
        'DEFAULT': '0',
      },
      boxShadow: {
        'geometric': '4px 4px 0px 0px rgba(0, 0, 0, 0.1)',
        'geometric-hover': '8px 8px 0px 0px rgba(0, 0, 0, 0.15)',
        'geometric-lg': '8px 8px 0px 0px rgba(0, 0, 0, 0.1)',
      },
    },
  },
  plugins: [
    require('@tailwindcss/forms'),
  ],
}
