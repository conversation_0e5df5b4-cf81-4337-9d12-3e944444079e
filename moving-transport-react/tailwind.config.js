/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        // Liquid Silver Monochromatic color palette
        'silver-light': '#E8E8E8',
        'silver': '#C0C0C0',
        'silver-dark': '#A8A8A8',
        'silver-darker': '#808080',
        'silver-darkest': '#606060',
        'light-bg': '#F5F5F5',
        'dark-text': '#2C2C2C',
        'secondary-text': '#4A4A4A',
      },
      fontFamily: {
        'sans': ['Inter', 'system-ui', 'sans-serif'],
        'display': ['Inter', 'system-ui', 'sans-serif'],
      },
      spacing: {
        // Ensure all spacing follows 4px grid system
        '18': '4.5rem',
        '22': '5.5rem',
        '26': '6.5rem',
        '30': '7.5rem',
      },
      borderRadius: {
        // Override all border radius to be sharp (none)
        'none': '0',
        'DEFAULT': '0',
      },
      boxShadow: {
        'geometric': '4px 4px 0px 0px rgba(0, 0, 0, 0.1)',
        'geometric-hover': '8px 8px 0px 0px rgba(0, 0, 0, 0.15)',
        'geometric-lg': '8px 8px 0px 0px rgba(0, 0, 0, 0.1)',
      },
      backgroundImage: {
        'liquid-silver': 'linear-gradient(135deg, #F5F5F5 0%, #E8E8E8 25%, #C0C0C0 50%, #E8E8E8 75%, #F5F5F5 100%)',
        'liquid-silver-subtle': 'linear-gradient(135deg, #F8F8F8 0%, #F0F0F0 50%, #F8F8F8 100%)',
      },
    },
  },
  plugins: [],
}
