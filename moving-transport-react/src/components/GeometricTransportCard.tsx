import type { TransportOption } from '../types/models';

interface GeometricTransportCardProps {
  transport: TransportOption;
  selected?: boolean;
  onClick?: () => void;
}

const GeometricTransportCard = ({ transport, selected = false, onClick }: GeometricTransportCardProps) => {
  return (
    <div
      onClick={onClick}
      className={`
        card-geometric h-full cursor-pointer transition-all duration-200
        ${selected 
          ? 'border-burnt-orange shadow-geometric-hover bg-light-bg' 
          : 'border-lemongrass hover:shadow-geometric-hover'
        }
      `}
    >
      {/* Image Section */}
      <div className="relative h-48 overflow-hidden">
        <img
          src={transport.image}
          alt={transport.title}
          className="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
        />
        <div className="absolute top-4 left-4 bg-dark-text text-white px-2 py-1 text-xs font-bold border border-lemongrass">
          {transport.type.toUpperCase()}
        </div>
        {transport.verified && (
          <div className="absolute top-4 right-4 bg-olive text-white p-1 border border-lemongrass">
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
          </div>
        )}
      </div>

      {/* Content Section */}
      <div className="p-4 space-y-4">
        {/* Title and Price Row */}
        <div className="flex justify-between items-start">
          <div>
            <h3 className="text-lg font-black text-dark-text leading-tight">
              {transport.title}
            </h3>
            <div className="flex items-center mt-1">
              <div className="flex text-burnt-orange">
                {[...Array(5)].map((_, i) => (
                  <svg
                    key={i}
                    className={`w-4 h-4 ${i < Math.floor(transport.rating) ? 'text-burnt-orange' : 'text-gray-300'}`}
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                ))}
              </div>
              <span className="ml-1 text-sm text-secondary-text font-medium">
                ({transport.rating})
              </span>
            </div>
          </div>
          <div className="text-right">
            <div className="text-2xl font-black text-olive">
              {transport.price}
            </div>
            <div className="text-xs text-secondary-text font-medium">
              per hour
            </div>
          </div>
        </div>

        {/* Location */}
        <div className="flex items-center text-secondary-text">
          <svg className="w-4 h-4 mr-2 text-burnt-orange" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
          </svg>
          <span className="text-sm font-medium">
            {transport.location} ({transport.distance} miles away)
          </span>
        </div>

        {/* Specifications Grid */}
        <div className="grid grid-cols-3 gap-4 pt-2 border-t-2 border-lemongrass">
          <div className="text-center">
            <div className="text-xs text-secondary-text font-medium">CAPACITY</div>
            <div className="text-sm font-bold text-dark-text">{transport.capacity}</div>
          </div>
          <div className="text-center">
            <div className="text-xs text-secondary-text font-medium">SIZE</div>
            <div className="text-sm font-bold text-dark-text">{transport.size}</div>
          </div>
          <div className="text-center">
            <div className="text-xs text-secondary-text font-medium">DRIVER</div>
            <div className="text-sm font-bold text-dark-text">
              {transport.driverIncluded ? 'INCLUDED' : 'SELF-DRIVE'}
            </div>
          </div>
        </div>

        {/* Book Button */}
        <button className="w-full btn-geometric text-center mt-4">
          BOOK NOW
        </button>
      </div>
    </div>
  );
};

export default GeometricTransportCard;
