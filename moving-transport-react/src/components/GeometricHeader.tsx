import { useState } from 'react';
import { Link as RouterLink } from 'react-router-dom';

const GeometricHeader = () => {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-white border-b-4 border-lemongrass">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <div className="flex items-center">
            <RouterLink to="/" className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-olive border-2 border-lemongrass flex items-center justify-center">
                <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M8 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM15 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0z"/>
                  <path d="M3 4a1 1 0 00-1 1v10a1 1 0 001 1h1.05a2.5 2.5 0 014.9 0H10a1 1 0 001-1V5a1 1 0 00-1-1H3zM14 7a1 1 0 00-1 1v6.05A2.5 2.5 0 0115.95 16H17a1 1 0 001-1V8a1 1 0 00-1-1h-3z"/>
                </svg>
              </div>
              <span className="text-xl font-black text-dark-text">MovingTransport</span>
            </RouterLink>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            {/* Navigation items removed as per requirements */}
          </nav>

          {/* Desktop CTA Buttons */}
          <div className="hidden md:flex items-center space-x-4">
            <RouterLink
              to="/list-vehicle"
              className="btn-geometric-secondary"
            >
              List Your Vehicle
            </RouterLink>
            <RouterLink
              to="/signup"
              className="btn-geometric"
            >
              Sign Up
            </RouterLink>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              className="p-2 border-2 border-lemongrass bg-white hover:bg-olive hover:text-white transition-colors duration-200"
              aria-label="Toggle menu"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                {mobileMenuOpen ? (
                  <path strokeLinecap="square" strokeLinejoin="miter" strokeWidth={3} d="M6 18L18 6M6 6l12 12" />
                ) : (
                  <path strokeLinecap="square" strokeLinejoin="miter" strokeWidth={3} d="M4 6h16M4 12h16M4 18h16" />
                )}
              </svg>
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {mobileMenuOpen && (
          <div className="md:hidden border-t-2 border-lemongrass bg-white">
            <div className="px-2 pt-2 pb-3 space-y-1">
              <div className="flex flex-col space-y-2 pt-4">
                <RouterLink
                  to="/list-vehicle"
                  className="btn-geometric-secondary w-full text-center"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  List Your Vehicle
                </RouterLink>
                <RouterLink
                  to="/signup"
                  className="btn-geometric w-full text-center"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  Sign Up
                </RouterLink>
              </div>
            </div>
          </div>
        )}
      </div>
    </header>
  );
};

export default GeometricHeader;
