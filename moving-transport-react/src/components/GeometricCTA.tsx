import { Link as RouterLink } from 'react-router-dom';

const GeometricCTA = () => {
  return (
    <section className="relative py-20 bg-dark-text overflow-hidden">
      {/* Geometric Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-10 left-10 w-24 h-24 border-4 border-lemongrass bg-olive opacity-30"></div>
        <div className="absolute top-20 right-20 w-16 h-16 border-4 border-burnt-orange bg-transparent"></div>
        <div className="absolute bottom-20 left-1/4 w-20 h-20 border-4 border-lemongrass bg-burnt-orange opacity-40"></div>
        <div className="absolute bottom-10 right-1/3 w-12 h-12 border-4 border-olive bg-transparent"></div>
      </div>

      <div className="relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 className="text-3xl sm:text-4xl lg:text-5xl font-black text-white mb-6 leading-tight">
          READY TO START
          <span className="block text-lemongrass">MOVING?</span>
        </h2>
        
        <p className="text-lg sm:text-xl font-bold text-gray-300 mb-12 max-w-2xl mx-auto">
          JOIN THOUSANDS OF SATISFIED CUSTOMERS WHO TRUST OUR PLATFORM FOR THEIR MOVING NEEDS.
        </p>

        <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
          <RouterLink
            to="/search"
            className="px-8 py-4 bg-lemongrass border-4 border-lemongrass text-dark-text font-black text-lg hover:bg-white hover:border-white transition-all duration-200 shadow-geometric hover:shadow-geometric-hover"
          >
            FIND TRANSPORT NOW
          </RouterLink>
          
          <RouterLink
            to="/list-vehicle"
            className="px-8 py-4 bg-transparent border-4 border-burnt-orange text-burnt-orange font-black text-lg hover:bg-burnt-orange hover:text-white transition-all duration-200"
          >
            LIST YOUR VEHICLE
          </RouterLink>
        </div>

        {/* Trust Indicators */}
        <div className="mt-16 pt-12 border-t-4 border-lemongrass">
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-8">
            <div className="flex items-center justify-center space-x-3">
              <div className="w-8 h-8 bg-lemongrass border-2 border-white flex items-center justify-center">
                <svg className="w-5 h-5 text-dark-text" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              </div>
              <span className="text-white font-bold">SECURE PAYMENTS</span>
            </div>
            
            <div className="flex items-center justify-center space-x-3">
              <div className="w-8 h-8 bg-burnt-orange border-2 border-white flex items-center justify-center">
                <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
                </svg>
              </div>
              <span className="text-white font-bold">24/7 SUPPORT</span>
            </div>
            
            <div className="flex items-center justify-center space-x-3">
              <div className="w-8 h-8 bg-olive border-2 border-white flex items-center justify-center">
                <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              </div>
              <span className="text-white font-bold">VERIFIED DRIVERS</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default GeometricCTA;
