@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* CSS Custom Properties for Tailwind CSS v4 */
@layer base {
  :root {
    --color-lemongrass: #9ACD32;
    --color-olive: #808000;
    --color-burnt-orange: #CC5500;
    --color-light-bg: #F9F9F9;
    --color-dark-text: #000000;
    --color-secondary-text: #333333;
  }
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: 'Inter', 'Segoe UI', 'Roboto', 'Helvetica', 'Arial', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--color-light-bg);
  color: var(--color-dark-text);
}

html {
  scroll-behavior: smooth;
}

/* Neo-brutalism base styles */
* {
  border-radius: 0 !important;
}

/* Geometric button styles */
@layer components {
  .btn-geometric {
    @apply border-2 bg-white text-black font-bold px-6 py-3 transition-all duration-200;
    border-color: var(--color-lemongrass);
    background-color: var(--color-olive);
    color: white;
  }

  .btn-geometric:hover {
    background-color: var(--color-burnt-orange);
    border-color: var(--color-burnt-orange);
  }

  .btn-geometric-secondary {
    @apply border-2 bg-transparent font-bold px-6 py-3 transition-all duration-200;
    border-color: var(--color-burnt-orange);
    color: var(--color-burnt-orange);
  }

  .btn-geometric-secondary:hover {
    background-color: var(--color-burnt-orange);
    color: white;
  }

  .card-geometric {
    @apply border-2 bg-white transition-all duration-200;
    border-color: var(--color-lemongrass);
    box-shadow: 4px 4px 0px 0px rgba(0, 0, 0, 0.1);
  }

  .card-geometric:hover {
    box-shadow: 8px 8px 0px 0px rgba(0, 0, 0, 0.15);
  }
}

/* Map styles */
.leaflet-map-container {
  width: 100%;
  height: 100%;
  min-height: 500px;
}
