@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: 'Inter', 'Segoe UI', 'Roboto', 'Helvetica', 'Arial', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: linear-gradient(135deg, #F8F8F8 0%, #F0F0F0 50%, #F8F8F8 100%);
  color: #2C2C2C;
  min-height: 100vh;
}

html {
  scroll-behavior: smooth;
}

/* Neo-brutalism base styles */
* {
  border-radius: 0 !important;
}

/* Geometric button styles */
@layer components {
  .btn-geometric {
    @apply border-2 border-silver bg-silver-dark text-white font-bold px-6 py-3 transition-all duration-200 hover:bg-silver-darkest hover:border-silver-darkest;
  }

  .btn-geometric-secondary {
    @apply border-2 border-silver-darker bg-transparent text-silver-darker font-bold px-6 py-3 transition-all duration-200 hover:bg-silver-darker hover:text-white;
  }

  .card-geometric {
    @apply border-2 border-silver bg-white shadow-geometric transition-all duration-200 hover:shadow-geometric-hover;
  }
}

/* Map styles */
.leaflet-map-container {
  width: 100%;
  height: 100%;
  min-height: 500px;
}
