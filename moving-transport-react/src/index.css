@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800;900&display=swap');

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: 'Inter', 'Segoe UI', 'Roboto', 'Helvetica', 'Arial', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #F9F9F9;
  color: #000000;
}

html {
  scroll-behavior: smooth;
}

/* Neo-brutalism base styles */
* {
  border-radius: 0 !important;
}

/* Geometric button styles */
@layer components {
  .btn-geometric {
    @apply border-2 border-lemongrass bg-olive text-white font-bold px-6 py-3 transition-all duration-200 hover:bg-burnt-orange hover:border-burnt-orange;
  }

  .btn-geometric-secondary {
    @apply border-2 border-burnt-orange bg-transparent text-burnt-orange font-bold px-6 py-3 transition-all duration-200 hover:bg-burnt-orange hover:text-white;
  }

  .card-geometric {
    @apply border-2 border-lemongrass bg-white shadow-geometric transition-all duration-200 hover:shadow-geometric-hover;
  }
}

/* Map styles */
.leaflet-map-container {
  width: 100%;
  height: 100%;
  min-height: 500px;
}
