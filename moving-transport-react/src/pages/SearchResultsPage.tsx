import GeometricSearchResults from '../components/GeometricSearchResults';

const SearchResultsPage = () => {
  return <GeometricSearchResults />;
};



  // Apply filters
  useEffect(() => {
    if (!loading) {
      let results = [...transportOptions];

      // Filter by price range
      results = results.filter(
        (option) =>
          option.pricePerHour >= filters.priceRange[0] &&
          option.pricePerHour <= filters.priceRange[1]
      );

      // Filter by distance
      results = results.filter(
        (option) => option.distance <= filters.distance
      );

      // Filter by vehicle types
      const selectedTypes = Object.entries(filters.vehicleTypes)
        .filter(([_, isSelected]) => isSelected)
        .map(([type]) => type);

      if (selectedTypes.length > 0) {
        results = results.filter((option) => {
          // Map filter types to actual data types
          const typeMap: Record<string, string[]> = {
            smallVan: ['Small Van'],
            largeVan: ['Large Van'],
            smallTruck: ['Small Truck'],
            largeTruck: ['Large Truck'],
            pickupTruck: ['Pickup Truck'],
            boxTruck: ['Box Truck']
          };

          return selectedTypes.some(type =>
            typeMap[type]?.includes(option.type)
          );
        });
      }

      // Sort results
      switch (filters.sortBy) {
        case 'price_low':
          results.sort((a, b) => a.pricePerHour - b.pricePerHour);
          break;
        case 'price_high':
          results.sort((a, b) => b.pricePerHour - a.pricePerHour);
          break;
        case 'rating':
          results.sort((a, b) => b.rating - a.rating);
          break;
        case 'distance':
          results.sort((a, b) => a.distance - b.distance);
          break;
        default:
          // Recommended - a combination of rating and distance
          results.sort((a, b) => (b.rating * 0.7 + (10 - b.distance) * 0.3) - (a.rating * 0.7 + (10 - a.distance) * 0.3));
      }

      setFilteredResults(results);
    }
  }, [filters, loading]);

  const handleFilterChange = (newFilters: FilterOptions) => {
    setFilters(newFilters);
  };

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // In a real app, this would trigger a new search
    setLoading(true);
    setTimeout(() => {
      setLoading(false);
    }, 1000);
  };

  const handleSearchChange = (query: string) => {
    setSearchQuery(query);
  };

  const handleTransportSelect = (transport: TransportOption) => {
    setSelectedTransport(transport.id === selectedTransport?.id ? null : transport);
  };

  const handleMarkerClick = (transport: TransportOption) => {
    setSelectedTransport(transport);
    // Scroll to the transport card if it's not visible
    const element = document.getElementById(`transport-${transport.id}`);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
    }
  };

  return (
    <Box>
      <Header />
      <Box sx={{
        pt: 10,
        pb: 8,
        bgcolor: 'background.default',
        minHeight: 'calc(100vh - 64px)',
      }}>
        <Container maxWidth="xl">
          <Breadcrumbs
            separator={<NavigateNextIcon fontSize="small" />}
            aria-label="breadcrumb"
            sx={{ mb: 3 }}
          >
            <Link component={RouterLink} to="/" color="inherit">
              Home
            </Link>
            <Typography color="text.primary">Search Results</Typography>
          </Breadcrumbs>

          <Box sx={{ mb: 4 }}>
            <SearchBar
              searchQuery={searchQuery}
              onSearchChange={handleSearchChange}
              onSearch={handleSearchSubmit}
              filters={filters}
              onFilterChange={handleFilterChange}
            />
          </Box>

          <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Box>
              <Typography variant="h5" component="h1" sx={{ fontWeight: 700 }}>
                {loading ? 'Searching...' : `${filteredResults.length} transport options found`}
              </Typography>
              {!loading && searchQuery && (
                <Typography variant="body1" color="text.secondary">
                  for "{searchQuery}"
                </Typography>
              )}
            </Box>
            <Box sx={{ display: 'flex', gap: 1 }}>
              {Object.entries(filters.vehicleTypes)
                .filter(([_, isSelected]) => isSelected)
                .map(([key]) => {
                  const label = key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
                  return (
                    <Chip
                      key={key}
                      label={label}
                      color="primary"
                      onDelete={() => {
                        const newVehicleTypes = { ...filters.vehicleTypes };
                        newVehicleTypes[key as keyof VehicleTypes] = false;
                        setFilters({
                          ...filters,
                          vehicleTypes: newVehicleTypes
                        });
                      }}
                      sx={{
                        background: 'linear-gradient(90deg, #6366F1 0%, #8B5CF6 100%)',
                        fontWeight: 500,
                      }}
                    />
                  );
                })
              }
              {filters.priceRange[0] > 0 || filters.priceRange[1] < 150 ? (
                <Chip
                  label={`$${filters.priceRange[0]} - $${filters.priceRange[1]}`}
                  color="primary"
                  onDelete={() => setFilters({
                    ...filters,
                    priceRange: [0, 150]
                  })}
                  sx={{
                    background: 'linear-gradient(90deg, #6366F1 0%, #8B5CF6 100%)',
                    fontWeight: 500,
                  }}
                />
              ) : null}
              {filters.distance !== 10 ? (
                <Chip
                  label={`Within ${filters.distance} miles`}
                  color="primary"
                  onDelete={() => setFilters({
                    ...filters,
                    distance: 10
                  })}
                  sx={{
                    background: 'linear-gradient(90deg, #6366F1 0%, #8B5CF6 100%)',
                    fontWeight: 500,
                  }}
                />
              ) : null}
            </Box>
          </Box>

          <Divider sx={{ mb: 3 }} />

          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', py: 8 }}>
              <CircularProgress sx={{ color: theme.palette.primary.main }} />
            </Box>
          ) : (
            <Grid container spacing={3} sx={{ height: 'calc(100vh - 250px)', minHeight: '600px' }}>
              <Grid size={{ xs: 12, md: 5, lg: 4 }} sx={{ height: '100%', overflow: 'auto', pr: 1 }}>
                {filteredResults.length > 0 ? (
                  <Box sx={{ pr: 2 }}>
                    {filteredResults.map((transport) => (
                      <Box id={`transport-${transport.id}`} key={transport.id}>
                        <TransportCard
                          transport={transport}
                          selected={selectedTransport?.id === transport.id}
                          onClick={() => handleTransportSelect(transport)}
                        />
                      </Box>
                    ))}
                  </Box>
                ) : (
                  <Paper elevation={0} sx={{ p: 4, textAlign: 'center', bgcolor: 'background.paper', borderRadius: 3 }}>
                    <Typography variant="h6">
                      No transport options found matching your criteria.
                    </Typography>
                    <Typography variant="body1" color="text.secondary" sx={{ mt: 1 }}>
                      Try adjusting your filters or search for a different location.
                    </Typography>
                  </Paper>
                )}
              </Grid>
              <Grid size={{ xs: 12, md: 7, lg: 8 }} sx={{ height: { xs: '500px', md: '100%' } }}>
                <Box sx={{
                  height: '100%',
                  minHeight: '500px',
                  borderRadius: 3,
                  overflow: 'hidden',
                  boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                  border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
                }}>
                  <MapComponent
                    transportOptions={filteredResults}
                    selectedTransport={selectedTransport}
                    onMarkerClick={handleMarkerClick}
                  />
                </Box>
              </Grid>
            </Grid>
          )}
        </Container>
      </Box>
      <Footer />
    </Box>
  );
};

export default SearchResultsPage;
