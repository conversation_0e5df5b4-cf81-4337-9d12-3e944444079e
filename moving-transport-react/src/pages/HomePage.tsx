import { useEffect } from 'react';
import GeometricHeader from '../components/GeometricHeader';
import GeometricHero from '../components/GeometricHero';
import GeometricCTA from '../components/GeometricCTA';
import GeometricFooter from '../components/GeometricFooter';

const HomePage = () => {
  // Scroll to top when component mounts
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  return (
    <div className="min-h-screen bg-light-bg">
      <GeometricHeader />
      <GeometricHero />
      <GeometricCTA />
      <GeometricFooter />
    </div>
  );
};

export default HomePage;
